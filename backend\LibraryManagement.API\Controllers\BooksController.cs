using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using LibraryManagement.Core.Interfaces;
using LibraryManagement.Core.Entities;
using LibraryManagement.Application.DTOs;
using Microsoft.EntityFrameworkCore;

namespace LibraryManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[AllowAnonymous] // Allow anonymous access for all endpoints
public class BooksController : ControllerBase
{
    private readonly IUnitOfWork _unitOfWork;

    public BooksController(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<BookDto>>> GetBooks()
    {
        var books = await _unitOfWork.Books.GetAllAsync();
        var categories = await _unitOfWork.Categories.GetAllAsync();
        var categoryDict = categories.ToDictionary(c => c.Id, c => c.Name);




        var bookDtos = books.Select(book => new BookDto
        {
            Id = book.Id,
            Title = book.Title,
            Author = book.Author,
            ISBN = book.ISBN,
            Publisher = book.Publisher,
            PublishedDate = book.PublishedDate,
            CategoryId = book.CategoryId,
            CategoryName = categoryDict.GetValueOrDefault(book.CategoryId, "Unknown"),
            Quantity = book.Quantity, // Tổng số lượng
            StockQuantity = book.StockQuantity, // Số lượng trong kho
            OnShelfQuantity = book.OnShelfQuantity, // Số lượng trên kệ
            AvailableQuantity = book.StockQuantity, // Deprecated - giữ để tương thích
            Description = book.Description,
            ImageUrl = book.ImageUrl,
            Price = book.Price,
            CreatedAt = book.CreatedAt,
            UpdatedAt = book.UpdatedAt,

            // ✅ Tổng số lượng thực tế = kho + trên kệ
            TotalQuantity = book.StockQuantity + book.OnShelfQuantity
        }).ToList();


        return Ok(bookDtos);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<BookDto>> GetBook(int id)
    {
        var book = await _unitOfWork.Books.GetByIdAsync(id);
        if (book == null)
        {
            return NotFound();
        }

        var category = await _unitOfWork.Categories.GetByIdAsync(book.CategoryId);
        var bookDto = new BookDto
        {
            Id = book.Id,
            Title = book.Title,
            Author = book.Author,
            ISBN = book.ISBN,
            Publisher = book.Publisher,
            PublishedDate = book.PublishedDate,
            CategoryId = book.CategoryId,
            CategoryName = category?.Name ?? "Unknown",
            Quantity = book.Quantity, // Tổng số lượng
            StockQuantity = book.StockQuantity, // Số lượng trong kho
            OnShelfQuantity = book.OnShelfQuantity, // Số lượng trên kệ
            AvailableQuantity = book.StockQuantity, // Deprecated
            Description = book.Description,
            ImageUrl = book.ImageUrl,
            Price = book.Price,
            CreatedAt = book.CreatedAt,
            UpdatedAt = book.UpdatedAt,

            TotalQuantity = book.StockQuantity
        };

        return Ok(bookDto);
    }

    [HttpPost]
    public async Task<ActionResult<BookDto>> CreateBook(CreateBookDto createBookDto)
    {
        var existingBook = await _unitOfWork.Books.FindAsync(b => b.ISBN == createBookDto.ISBN);
        if (existingBook.Any())
        {
            return BadRequest("ISBN đã tồn tại. Không thể tạo sách trùng mã ISBN.");
        }

        // Validate category exists
        var categoryExists = await _unitOfWork.Categories.ExistsAsync(createBookDto.CategoryId);
        if (!categoryExists)
        {
            return BadRequest("Invalid category ID");
        }

        var book = new Book
        {
            Title = createBookDto.Title,
            Author = createBookDto.Author,
            ISBN = createBookDto.ISBN,
            Publisher = createBookDto.Publisher,
            PublishedDate = createBookDto.PublishedDate,
            CategoryId = createBookDto.CategoryId,
            Quantity = createBookDto.Quantity,
            Description = createBookDto.Description,
            ImageUrl = createBookDto.ImageUrl,
            Price = createBookDto.Price,
            StockQuantity = createBookDto.Quantity
        };

        await _unitOfWork.Books.AddAsync(book);
        await _unitOfWork.SaveChangesAsync();

        var category = await _unitOfWork.Categories.GetByIdAsync(book.CategoryId);

        var bookDto = new BookDto
        {
            Id = book.Id,
            Title = book.Title,
            Author = book.Author,
            ISBN = book.ISBN,
            Publisher = book.Publisher,
            PublishedDate = book.PublishedDate,
            CategoryId = book.CategoryId,
            CategoryName = category?.Name ?? "Unknown",
            Quantity = book.Quantity,
            Description = book.Description,
            ImageUrl = book.ImageUrl,
            Price = book.Price,
            CreatedAt = book.CreatedAt,
            UpdatedAt = book.UpdatedAt
        };

        return CreatedAtAction(nameof(GetBook), new { id = book.Id }, bookDto);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateBook(int id, UpdateBookDto updateBookDto)
    {
        var book = await _unitOfWork.Books.GetByIdAsync(id);
        if (book == null)
        {
            return NotFound();
        }

        var isbnConflict = await _unitOfWork.Books
            .FindAsync(b => b.ISBN == updateBookDto.ISBN && b.Id != id);
        if (isbnConflict.Any())
        {
            return BadRequest("ISBN đã được sử dụng cho một sách khác.");
        }

        // Validate category exists
        var categoryExists = await _unitOfWork.Categories.ExistsAsync(updateBookDto.CategoryId);
        if (!categoryExists)
        {
            return BadRequest("Invalid category ID");
        }

        // ✅ Tính phần thay đổi số lượng
        int oldQuantity = book.Quantity;
        int newQuantity = updateBookDto.Quantity;
        int delta = newQuantity - oldQuantity;

        // ✅ Tự cập nhật kho dựa vào phần thay đổi
        book.StockQuantity = Math.Max(0, book.StockQuantity + delta);

        // ✅ Cập nhật các thông tin khác
        book.Quantity = newQuantity;
        book.Title = updateBookDto.Title;
        book.Author = updateBookDto.Author;
        book.ISBN = updateBookDto.ISBN;
        book.Publisher = updateBookDto.Publisher;
        book.PublishedDate = updateBookDto.PublishedDate;
        book.CategoryId = updateBookDto.CategoryId;
        book.Description = updateBookDto.Description;
        book.ImageUrl = updateBookDto.ImageUrl;
        book.Price = updateBookDto.Price;

        await _unitOfWork.Books.UpdateAsync(book);
        await _unitOfWork.SaveChangesAsync();

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteBook(int id)
    {
        var book = await _unitOfWork.Books.GetByIdAsync(id);
        if (book == null)
        {
            return NotFound();
        }

        // Check if book has active borrow records
        var activeBorrows = await _unitOfWork.BorrowRecords.FindAsync(br =>
            br.BookId == id && br.ReturnDate == null);

        if (activeBorrows.Any())
        {
            return BadRequest("Không thể xóa sách đang được mượn");
        }

        await _unitOfWork.Books.DeleteAsync(book);
        await _unitOfWork.SaveChangesAsync();

        return NoContent();
    }

    [HttpPost("delete-multiple")]
    public async Task<IActionResult> DeleteMultipleBooks([FromBody] int[] bookIds)
    {
        if (bookIds == null || bookIds.Length == 0)
        {
            return BadRequest("Danh sách ID sách không được để trống");
        }

        var failedDeletes = new List<string>();
        var successCount = 0;

        foreach (var bookId in bookIds)
        {
            var book = await _unitOfWork.Books.GetByIdAsync(bookId);
            if (book == null)
            {
                failedDeletes.Add($"Sách ID {bookId}: Không tìm thấy");
                continue;
            }

            // Check if book has active borrow records
            var activeBorrows = await _unitOfWork.BorrowRecords.FindAsync(br =>
                br.BookId == bookId && br.ReturnDate == null);

            if (activeBorrows.Any())
            {
                failedDeletes.Add($"Sách '{book.Title}': Đang được mượn");
                continue;
            }

            await _unitOfWork.Books.DeleteAsync(book);
            successCount++;
        }

        await _unitOfWork.SaveChangesAsync();

        var result = new
        {
            SuccessCount = successCount,
            FailedCount = failedDeletes.Count,
            FailedReasons = failedDeletes,
            Message = $"Đã xóa thành công {successCount} sách" + 
                     (failedDeletes.Count > 0 ? $", {failedDeletes.Count} sách không thể xóa" : "")
        };

        return Ok(result);
    }

    [HttpGet("search")]
    public async Task<ActionResult<IEnumerable<BookDto>>> SearchBooks([FromQuery] string? title, [FromQuery] string? author, [FromQuery] int? categoryId)
    {
        var books = await _unitOfWork.Books.GetAllAsync();
        var categories = await _unitOfWork.Categories.GetAllAsync();
        var categoryDict = categories.ToDictionary(c => c.Id, c => c.Name);

        var filteredBooks = books.AsEnumerable();

        if (!string.IsNullOrEmpty(title))
        {
            filteredBooks = filteredBooks.Where(b => b.Title.Contains(title, StringComparison.OrdinalIgnoreCase));
        }

        if (!string.IsNullOrEmpty(author))
        {
            filteredBooks = filteredBooks.Where(b => b.Author.Contains(author, StringComparison.OrdinalIgnoreCase));
        }

        if (categoryId.HasValue)
        {
            filteredBooks = filteredBooks.Where(b => b.CategoryId == categoryId.Value);
        }

        var bookDtos = filteredBooks.Select(book => new BookDto
        {
            Id = book.Id,
            Title = book.Title,
            Author = book.Author,
            ISBN = book.ISBN,
            Publisher = book.Publisher,
            PublishedDate = book.PublishedDate,
            CategoryId = book.CategoryId,
            CategoryName = categoryDict.GetValueOrDefault(book.CategoryId, "Unknown"),
            Quantity = book.Quantity,
            AvailableQuantity = book.StockQuantity,
            Description = book.Description,
            ImageUrl = book.ImageUrl,
            Price = book.Price,
            CreatedAt = book.CreatedAt,
            UpdatedAt = book.UpdatedAt
        }).ToList();

        return Ok(bookDtos);
    }

    public class AssignShelfRequest
    {
        public int Quantity { get; set; }
    }

    [HttpGet("in-storage")]
    public async Task<IActionResult> GetBooksInStorage()
    {
        var booksInStorage = await _unitOfWork.Books
            .Query()
            .Where(b => b.StockQuantity > 0)
            .Select(b => new
            {
                b.Id,
                b.Title,
                b.Author,
                b.ISBN,
                b.StockQuantity
            })
            .ToListAsync();

        return Ok(booksInStorage);
    }

    [HttpGet("on-shelf")]
    public async Task<IActionResult> GetBooksOnShelf()
    {
        var booksOnShelf = await _unitOfWork.Books
            .Query()
            .Include(b => b.Bookshelf)
            .ThenInclude(s => s.Zone)
            .Where(b => b.BookshelfId != null)
            .Select(b => new
            {
                Id = b.Id,
                Title = b.Title,
                Author = b.Author,
                ISBN = b.ISBN,
                StockQuantity = b.StockQuantity,
                Location = $"{b.Bookshelf.Zone.Name} > {b.Bookshelf.Name}" + 
                          (b.LocationCode != null ? $" > {b.LocationCode}" : "")
            })
            .ToListAsync();

        return Ok(booksOnShelf);
    }

}