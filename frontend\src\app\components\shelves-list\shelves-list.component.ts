import { Component, OnInit } from "@angular/core";
import { CommonModule } from "@angular/common";
import { MatCardModule } from "@angular/material/card";
import { MatTableModule } from "@angular/material/table";
import { MatButtonModule } from "@angular/material/button";
import { MatIconModule } from "@angular/material/icon";
import { MatChipsModule } from "@angular/material/chips";
import { MatSnackBarModule, MatSnackBar } from "@angular/material/snack-bar";
import { MatTooltipModule } from "@angular/material/tooltip";

import { ShelfService } from "src/app/services/shelf.service";
import { Shelf } from "src/app/models/shelf.model";
import { ZoneService, Zone } from "src/app/services/zone.service";
import { Router, RouterModule } from "@angular/router";

@Component({
  selector: "app-shelves-list",
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatSnackBarModule,
    MatTooltipModule,
  ],
  template: `
    <div class="shelves-container">
      <!-- Header Section -->
      <div class="page-header">
        <div class="header-content">
          <h1 class="page-title">
            <mat-icon>inventory_2</mat-icon>
            Quản lý Kệ Sách
          </h1>
          <p class="page-subtitle">Tổng số: {{ shelves.length }} kệ</p>
        </div>
        <div class="header-actions">
          <button mat-stroked-button color="primary" (click)="goToZoneList()">
            <mat-icon>map</mat-icon>
            Quản lý khu vực
          </button>
          <button mat-raised-button color="primary" (click)="addShelf()">
            <mat-icon>add</mat-icon>
            Thêm kệ
          </button>
        </div>
      </div>

      <!-- Content Card -->
      <mat-card class="content-card">
        <mat-card-content>
          <table
            mat-table
            [dataSource]="shelves"
            class="shelves-table"
          >
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef>Tên kệ</th>
            <td mat-cell *matCellDef="let s">{{ s.name }}</td>
          </ng-container>

          <ng-container matColumnDef="zone">
            <th mat-header-cell *matHeaderCellDef>Khu vực</th>
            <td mat-cell *matCellDef="let s">{{ getZoneName(s.zoneId) }}</td>
          </ng-container>

          <ng-container matColumnDef="capacity">
            <th mat-header-cell *matHeaderCellDef>Sức chứa</th>
            <td mat-cell *matCellDef="let s">{{ s.capacity }}</td>
          </ng-container>

          <ng-container matColumnDef="currentCount">
            <th mat-header-cell *matHeaderCellDef>Hiện có</th>
            <td mat-cell *matCellDef="let s">
              {{ s.currentCount }} / {{ s.capacity }} sách
            </td>
          </ng-container>

          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>Trạng thái</th>
            <td mat-cell *matCellDef="let s">
              <mat-chip [color]="getStatusColor(s.status)" selected>
                {{ getStatusLabel(s.status) }}
              </mat-chip>
            </td>
          </ng-container>

          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Thao tác</th>
            <td mat-cell *matCellDef="let s">
              <button mat-icon-button color="primary"
                      [routerLink]="['/shelves', s.id]"
                      matTooltip="Xem chi tiết">
                <mat-icon>visibility</mat-icon>
              </button>
              <button mat-icon-button color="accent"
                      [routerLink]="['/shelves/edit', s.id]"
                      matTooltip="Chỉnh sửa">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button color="warn"
                      [routerLink]="['/shelves', s.id, 'books']"
                      matTooltip="Quản lý sách">
                <mat-icon>book</mat-icon>
              </button>
              <button mat-icon-button color="warn"
                      (click)="deleteShelf(s)"
                      matTooltip="Xóa">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .shelves-container {
      padding: 24px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 24px;
      gap: 24px;

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: stretch;
      }
    }

    .header-content {
      flex: 1;
    }

    .page-title {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
      color: var(--color-on-background);

      mat-icon {
        font-size: 32px;
        width: 32px;
        height: 32px;
        color: var(--color-primary);
      }
    }

    .page-subtitle {
      margin: 0;
      color: var(--color-on-background);
      opacity: 0.7;
      font-size: 16px;
    }

    .header-actions {
      display: flex;
      gap: 12px;
      align-items: center;

      @media (max-width: 768px) {
        justify-content: flex-end;
      }
    }

    .content-card {
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .shelves-table {
      width: 100%;

      .mat-mdc-header-cell {
        font-weight: 600;
        color: var(--color-on-surface);
      }

      .mat-mdc-cell {
        color: var(--color-on-surface);
      }
    }
  `],
})
export class ShelvesListComponent implements OnInit {
  shelves: Shelf[] = [];
  zones: Zone[] = [];
  displayedColumns: string[] = [
    "name",
    "zone",
    "capacity",
    "currentCount",
    "status",
    "actions",
  ];

  constructor(
    private shelfService: ShelfService,
    private zoneService: ZoneService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadZones();
    this.loadShelves();
  }

  loadShelves(): void {
    this.shelfService.getAllShelves().subscribe({
      next: (data) => {
        this.shelves = data;
      },
      error: (err) => {
        console.error("Lỗi khi tải danh sách kệ:", err);
        this.snackBar.open('Lỗi khi tải danh sách kệ', 'Đóng', { duration: 3000 });
      },
    });
  }

  loadZones(): void {
    this.zoneService.getAllZones().subscribe({
      next: (zones) => (this.zones = zones),
      error: (err) => {
        console.error("Lỗi tải khu vực:", err);
        this.snackBar.open('Lỗi khi tải danh sách khu vực', 'Đóng', { duration: 3000 });
      },
    });
  }

  getZoneName(zoneId: number): string {
    const zone = this.zones.find((z) => z.id === zoneId);
    return zone ? zone.name : "Không rõ";
  }

  getStatusLabel(status: string): string {
    switch (status) {
      case "Active":
        return "Đang sử dụng";
      case "Full":
        return "Đã đầy";
      case "Maintenance":
        return "Bảo trì";
      default:
        return status;
    }
  }

  getStatusColor(status: string): string {
    switch (status) {
      case "Active":
        return "primary";
      case "Full":
        return "warn";
      case "Maintenance":
        return "accent";
      default:
        return "";
    }
  }

  editShelf(shelf: Shelf): void {
    this.router.navigate(["/shelves/edit", shelf.id]);
  }

  deleteShelf(shelf: Shelf): void {
    if (confirm(`Bạn có chắc chắn muốn xóa kệ "${shelf.name}"?`)) {
      this.shelfService.deleteShelf(shelf.id).subscribe({
        next: () => {
          this.snackBar.open('Xóa kệ thành công', 'Đóng', { duration: 3000 });
          this.loadShelves();
        },
        error: (err) => {
          console.error('Lỗi khi xóa kệ:', err);
          this.snackBar.open(err.error?.message || 'Lỗi khi xóa kệ', 'Đóng', { duration: 3000 });
        }
      });
    }
  }

  goToZoneList(): void {
    this.router.navigate(["/zones"]);
  }

  addShelf(): void {
    this.router.navigate(["/shelves/add"]);
  }
}
