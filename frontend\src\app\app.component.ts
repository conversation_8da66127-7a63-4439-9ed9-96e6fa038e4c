import { Compo<PERSON>, <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON>hild, HostListener } from "@angular/core";
import { CommonModule } from "@angular/common";
import {
  RouterOutlet,
  RouterModule,
  Router,
  NavigationEnd,
} from "@angular/router";
import { MatToolbarModule } from "@angular/material/toolbar";
import { MatButtonModule } from "@angular/material/button";
import { MatIconModule } from "@angular/material/icon";
import { MatSidenavModule, MatSidenav } from "@angular/material/sidenav";
import { MatListModule } from "@angular/material/list";
import { MatMenuModule } from "@angular/material/menu";
import { MatDividerModule } from "@angular/material/divider";
import { MatSnackBar, MatSnackBarModule } from "@angular/material/snack-bar";
import { MatBadgeModule } from "@angular/material/badge";
import { MatTooltipModule } from "@angular/material/tooltip";
import { AuthService } from "./services/auth.service";
import { ThemeService } from "./services/theme.service";
import { User } from "./models/auth.model";
import { NotAuthenticatedComponent } from "./components/not-authenticated/not-authenticated.component";
import { HasPermissionDirective } from "./directives/has-permission.directive";
import { ThemeToggleComponent } from "./shared/theme-toggle/theme-toggle.component";
import { BreadcrumbComponent } from "./shared/breadcrumb/breadcrumb.component";
import { filter } from "rxjs";

@Component({
  selector: "app-root",
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    RouterModule,
    MatToolbarModule,
    MatButtonModule,
    MatIconModule,
    MatSidenavModule,
    MatListModule,
    MatMenuModule,
    MatDividerModule,
    MatSnackBarModule,
    MatBadgeModule,
    MatTooltipModule,
    NotAuthenticatedComponent,
    HasPermissionDirective,
    ThemeToggleComponent,
    BreadcrumbComponent,
  ],
  template: `
    <mat-toolbar color="primary" class="app-toolbar">
      <div class="toolbar-left">
        <button
          mat-icon-button
          (click)="toggleSidenav()"
          *ngIf="currentUser && showMainLayout"
          matTooltip="Menu"
          class="menu-button"
        >
          <mat-icon>menu</mat-icon>
        </button>
        <div class="app-title">
          <mat-icon class="app-icon">local_library</mat-icon>
          <span class="title-text">Quản lý Thư viện</span>
        </div>
      </div>

      <div class="toolbar-right">
        <!-- Theme Toggle -->
        <app-theme-toggle *ngIf="currentUser"></app-theme-toggle>

        <!-- Notifications -->
        <button
          mat-icon-button
          *ngIf="currentUser"
          matTooltip="Thông báo"
          class="notification-button">
          <mat-icon matBadge="3" matBadgeColor="warn" matBadgeSize="small">notifications</mat-icon>
        </button>

        <!-- User Menu -->
        <div *ngIf="currentUser; else loginButton" class="user-section">
          <button mat-button [matMenuTriggerFor]="userMenu" class="user-button">
            <div class="user-avatar">
              <mat-icon>account_circle</mat-icon>
            </div>
            <div class="user-info" *ngIf="!isMobile">
              <div class="user-name">{{ currentUser.fullName }}</div>
              <div class="user-role">{{ currentUser.role }}</div>
            </div>
            <mat-icon class="dropdown-icon" *ngIf="!isMobile">arrow_drop_down</mat-icon>
          </button>
          <mat-menu #userMenu="matMenu" class="user-menu">
            <div class="user-menu-header">
              <div class="user-avatar-large">
                <mat-icon>account_circle</mat-icon>
              </div>
              <div class="user-details">
                <div class="user-name">{{ currentUser.fullName }}</div>
                <div class="user-email">{{ currentUser.email }}</div>
                <div class="user-role-badge">{{ currentUser.role }}</div>
              </div>
            </div>

            <mat-divider></mat-divider>

            <button mat-menu-item routerLink="/profile">
              <mat-icon>person</mat-icon>
              <span>Hồ sơ cá nhân</span>
            </button>

            <button mat-menu-item routerLink="/settings">
              <mat-icon>settings</mat-icon>
              <span>Cài đặt</span>
            </button>

            <div *ngIf="!currentUser.emailVerified" class="verification-section">
              <mat-divider></mat-divider>
              <div class="verification-warning">
                <mat-icon>warning</mat-icon>
                <span>Email chưa xác nhận</span>
              </div>

              <button mat-menu-item (click)="resendVerificationEmail()">
                <mat-icon>refresh</mat-icon>
                <span>Gửi lại email xác nhận</span>
              </button>

              <button mat-menu-item (click)="showDebugInfo()">
                <mat-icon>bug_report</mat-icon>
                <span>Debug Info</span>
              </button>

              <button mat-menu-item (click)="syncWithToken()">
                <mat-icon>sync</mat-icon>
                <span>Sync với Token</span>
              </button>
            </div>

            <mat-divider></mat-divider>
            <button mat-menu-item (click)="logout()" class="logout-item">
              <mat-icon>logout</mat-icon>
              <span>Đăng xuất</span>
            </button>
          </mat-menu>
        </div>

        <ng-template #loginButton>
          <button mat-raised-button color="accent" routerLink="/login" *ngIf="!showMainLayout" class="login-button">
            <mat-icon>login</mat-icon>
            <span>Đăng nhập</span>
          </button>
        </ng-template>
      </div>
    </mat-toolbar>

    <!-- Main App Layout for Authenticated Users -->
    <mat-sidenav-container
      class="sidenav-container"
      *ngIf="currentUser && showMainLayout"
    >
      <mat-sidenav
        #sidenav
        [mode]="isMobile ? 'over' : 'side'"
        [opened]="!isMobile"
        class="sidenav"
        [fixedInViewport]="isMobile"
        [fixedTopGap]="isMobile ? 64 : 0"
      >
        <div class="sidenav-content">
          <!-- User Info Section -->
          <div class="sidenav-user-info" *ngIf="!isMobile">
            <div class="user-avatar">
              <mat-icon>account_circle</mat-icon>
            </div>
            <div class="user-details">
              <div class="user-name">{{ currentUser.fullName }}</div>
              <div class="user-role">{{ currentUser.role }}</div>
            </div>
          </div>

          <mat-divider *ngIf="!isMobile"></mat-divider>

          <mat-nav-list class="nav-list">
            <!-- Common Navigation -->
            <div class="nav-section">
              <h3 mat-subheader class="nav-section-title">
                <mat-icon>dashboard</mat-icon>
                Tổng quan
              </h3>
              <a mat-list-item routerLink="/dashboard" routerLinkActive="active" class="nav-item">
                <mat-icon matListItemIcon>analytics</mat-icon>
                <span matListItemTitle>Dashboard</span>
              </a>
            </div>

            <!-- Admin Menu -->
            <div *ngIf="currentUser?.role === 'Admin'" class="nav-section">
              <h3 mat-subheader class="nav-section-title">
                <mat-icon>admin_panel_settings</mat-icon>
                Quản trị
              </h3>
              <a mat-list-item routerLink="/users" routerLinkActive="active" class="nav-item">
                <mat-icon matListItemIcon>manage_accounts</mat-icon>
                <span matListItemTitle>Người dùng</span>
              </a>
            </div>

            <!-- Library Management -->
            <div *ngIf="currentUser?.role === 'Admin' || currentUser?.role === 'Librarian'" class="nav-section">
              <h3 mat-subheader class="nav-section-title">
                <mat-icon>local_library</mat-icon>
                Quản lý thư viện
              </h3>
              <a mat-list-item routerLink="/books" routerLinkActive="active" class="nav-item">
                <mat-icon matListItemIcon>book</mat-icon>
                <span matListItemTitle>Sách</span>
              </a>
              <a mat-list-item routerLink="/categories" routerLinkActive="active" class="nav-item">
                <mat-icon matListItemIcon>category</mat-icon>
                <span matListItemTitle>Thể loại</span>
              </a>
              <a mat-list-item routerLink="/members" routerLinkActive="active" class="nav-item">
                <mat-icon matListItemIcon>people</mat-icon>
                <span matListItemTitle>Thành viên</span>
              </a>
              <a mat-list-item routerLink="/shelves" routerLinkActive="active" class="nav-item">
                <mat-icon matListItemIcon>inventory</mat-icon>
                <span matListItemTitle>Kệ sách</span>
              </a>
            </div>

            <!-- Operations -->
            <div class="nav-section">
              <h3 mat-subheader class="nav-section-title">
                <mat-icon>assignment</mat-icon>
                Hoạt động
              </h3>
              <a mat-list-item routerLink="/borrows" routerLinkActive="active" class="nav-item">
                <mat-icon matListItemIcon>assignment_turned_in</mat-icon>
                <span matListItemTitle>Mượn/Trả sách</span>
              </a>
            </div>

            <!-- Reports -->
            <div class="nav-section">
              <h3 mat-subheader class="nav-section-title">
                <mat-icon>assessment</mat-icon>
                Báo cáo
              </h3>
              <a mat-list-item routerLink="/reports/overdue-books" routerLinkActive="active" class="nav-item">
                <mat-icon matListItemIcon>warning</mat-icon>
                <span matListItemTitle>Sách quá hạn</span>
              </a>
              <a mat-list-item routerLink="/reports/fine-collection" routerLinkActive="active" class="nav-item">
                <mat-icon matListItemIcon>payments</mat-icon>
                <span matListItemTitle>Thu phí phạt</span>
              </a>
              <a mat-list-item routerLink="/reports/custom" routerLinkActive="active" class="nav-item">
                <mat-icon matListItemIcon>tune</mat-icon>
                <span matListItemTitle>Báo cáo tùy chỉnh</span>
              </a>
            </div>
          </mat-nav-list>
        </div>
      </mat-sidenav>

      <mat-sidenav-content class="main-content">
        <!-- Breadcrumb Navigation -->
        <app-breadcrumb [items]="getBreadcrumbItems()"></app-breadcrumb>

        <!-- Main Content -->
        <div class="content-wrapper">
          <router-outlet></router-outlet>
        </div>
      </mat-sidenav-content>
    </mat-sidenav-container>

    <!-- Auth Pages Layout -->
    <div class="auth-content" *ngIf="!currentUser && isAuthPage">
      <router-outlet></router-outlet>
    </div>

    <!-- Not Authenticated Component -->
    <app-not-authenticated
      *ngIf="!currentUser && !isAuthPage"
    ></app-not-authenticated>
  `,
  styles: [`
    .app-toolbar {
      position: sticky;
      top: 0;
      z-index: 1000;
      box-shadow: var(--shadow-md);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      margin-left: auto;
    }

    .app-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);

      .app-icon {
        font-size: 28px;
        width: 28px;
        height: 28px;
      }

      .title-text {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-semibold);

        @media (max-width: 768px) {
          display: none;
        }
      }
    }

    .menu-button {
      transition: transform var(--transition-fast);

      &:hover {
        transform: scale(1.1);
      }
    }

    .notification-button {
      transition: transform var(--transition-fast);

      &:hover {
        transform: scale(1.1);
      }
    }

    .user-section {
      .user-button {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-large);
        transition: all var(--transition-fast);
        min-height: 48px;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }

        .user-avatar {
          flex-shrink: 0;

          mat-icon {
            font-size: 32px;
            width: 32px;
            height: 32px;
            color: var(--color-on-primary);
          }
        }

        .user-info {
          display: flex;
          flex-direction: row;
          align-items: center;
          gap: var(--spacing-xs);
          min-width: 0;
          flex: 1;

          .user-name {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            line-height: 1.2;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100px;
          }

          .user-role {
            font-size: var(--font-size-xs);
            opacity: 0.9;
            line-height: 1.2;
            white-space: nowrap;
            padding: 2px 6px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-small);
          }
        }

        .dropdown-icon {
          flex-shrink: 0;
          transition: transform var(--transition-fast);
          color: var(--color-on-primary);
        }

        &[aria-expanded="true"] .dropdown-icon {
          transform: rotate(180deg);
        }
      }
    }

    .login-button {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
    }

    .sidenav-container {
      height: calc(100vh - 64px);
      background-color: var(--color-background);
    }

    .sidenav {
      width: 280px;
      background-color: var(--color-surface);
      border-right: 1px solid var(--color-outline);
      box-shadow: var(--shadow-md);

      @media (max-width: 768px) {
        width: 100vw;
        max-width: 320px;
      }
    }

    .sidenav-content {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .sidenav-user-info {
      padding: var(--spacing-lg);
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      background: linear-gradient(135deg, var(--color-primary), var(--color-primary-variant));
      color: var(--color-on-primary) !important;

      .user-avatar {
        mat-icon {
          font-size: 48px;
          width: 48px;
          height: 48px;
          color: var(--color-on-primary) !important;
        }
      }

      .user-details {
        .user-name {
          font-size: var(--font-size-lg);
          font-weight: var(--font-weight-semibold);
          margin-bottom: var(--spacing-xs);
          color: var(--color-on-primary) !important;
        }

        .user-role {
          font-size: var(--font-size-sm);
          opacity: 0.9;
          color: var(--color-on-primary) !important;
        }
      }
    }

    .nav-list {
      flex: 1;
      padding: var(--spacing-md) 0;
    }

    .nav-section {
      margin-bottom: var(--spacing-lg);

      .nav-section-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-semibold);
        color: var(--color-primary) !important;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
          color: var(--color-primary) !important;
        }
      }
    }

    .nav-item {
      margin: var(--spacing-xs) var(--spacing-md);
      border-radius: var(--radius-medium);
      transition: all var(--transition-fast);
      color: var(--color-on-surface) !important;

      &:hover {
        background-color: var(--color-surface-variant);
        transform: translateX(4px);
        color: var(--color-on-surface) !important;
      }

      &.active {
        background-color: var(--color-primary);
        color: var(--color-on-primary) !important;

        mat-icon {
          color: var(--color-on-primary) !important;
        }
      }

      mat-icon {
        transition: color var(--transition-fast);
        color: var(--color-on-surface) !important;
      }

      span {
        color: inherit !important;
      }
    }

    .main-content {
      height: 100%;
      overflow: auto;
      background-color: var(--color-background);
      display: flex;
      flex-direction: column;
    }

    .content-wrapper {
      flex: 1;
      overflow: auto;
    }

    .auth-content {
      height: calc(100vh - 64px);
      overflow: auto;
      background-color: var(--color-background);
    }

    .user-menu {
      min-width: 300px;
      max-width: 350px;

      .user-menu-header {
        padding: var(--spacing-lg);
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        background: linear-gradient(135deg, var(--color-primary), var(--color-primary-variant));
        color: var(--color-on-primary);
        margin: calc(-1 * var(--spacing-sm));
        margin-bottom: var(--spacing-sm);

        .user-avatar-large {
          flex-shrink: 0;

          mat-icon {
            font-size: 48px;
            width: 48px;
            height: 48px;
            color: var(--color-on-primary);
          }
        }

        .user-details {
          flex: 1;
          min-width: 0;

          .user-name {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            margin-bottom: var(--spacing-xs);
            color: var(--color-on-primary);
            word-wrap: break-word;
            line-height: 1.3;
          }

          .user-email {
            font-size: var(--font-size-sm);
            opacity: 0.9;
            margin-bottom: var(--spacing-xs);
            color: var(--color-on-primary);
            word-wrap: break-word;
            line-height: 1.3;
          }

          .user-role-badge {
            background-color: rgba(255, 255, 255, 0.2);
            color: var(--color-on-primary);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-small);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);
            display: inline-block;
          }
        }
      }

      // Menu items styling
      .mat-mdc-menu-item {
        color: var(--color-on-surface) !important;

        mat-icon {
          color: var(--color-on-surface) !important;
        }

        &:hover {
          background-color: var(--color-surface-variant) !important;
        }
      }

      .verification-section {
        .verification-warning {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);
          padding: var(--spacing-sm) var(--spacing-md);
          background-color: var(--color-warning);
          color: var(--color-on-primary);
          font-size: var(--font-size-sm);
          margin: var(--spacing-sm) 0;
          border-radius: var(--radius-small);
        }
      }

      .logout-item {
        color: var(--color-error) !important;

        mat-icon {
          color: var(--color-error) !important;
        }

        &:hover {
          background-color: rgba(var(--color-error), 0.1) !important;
        }
      }
    }

    @media (max-width: 768px) {
      .isMobile {
        .sidenav {
          width: 100vw;
          max-width: 320px;
        }
      }
    }
  `],
})
export class AppComponent implements OnInit {
  title = "Library Management System";
  currentUser: User | null = null;
  isAuthPage = false;
  showMainLayout = false;
  isMobile = false;

  @ViewChild("sidenav") sidenav!: MatSidenav;

  constructor(
    private authService: AuthService,
    private themeService: ThemeService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    this.checkScreenSize();
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenSize();
  }

  ngOnInit(): void {
    this.authService.currentUser$.subscribe((user) => {
      this.currentUser = user;
      this.updateLayoutState();
    });

    // Listen to route changes to determine if we're on auth pages
    this.router.events
      .pipe(
        filter(
          (event): event is NavigationEnd => event instanceof NavigationEnd
        )
      )
      .subscribe((event) => {
        this.isAuthPage = this.checkIfAuthPage(event.urlAfterRedirects);
        this.updateLayoutState();
      });

    // Initial check
    this.isAuthPage = this.checkIfAuthPage(this.router.url);
    this.updateLayoutState();
  }

  private checkScreenSize(): void {
    this.isMobile = window.innerWidth < 768;
  }

  toggleSidenav(): void {
    if (this.sidenav) {
      this.sidenav.toggle();
    }
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(["/login"]);
  }

  private checkIfAuthPage(url: string): boolean {
    const authPages = [
      "/login",
      "/register",
      "/verify-email",
      "/reset-password",
      "/forgot-password",
    ];
    return authPages.some((page) => url.startsWith(page));
  }

  private updateLayoutState(): void {
    this.showMainLayout = !!this.currentUser && !this.isAuthPage;

    // Auto redirect authenticated users away from auth pages
    if (
      this.currentUser &&
      this.isAuthPage &&
      this.router.url !== "/verify-email"
    ) {
      this.router.navigate(["/books"]);
    }
  }

  showDebugInfo(): void {
    const tokenInfo = this.authService.getTokenInfo();
    const user = this.currentUser;

    let message = `Debug Info:\n`;
    message += `- Email: ${user?.email}\n`;
    message += `- EmailVerified in localStorage: ${user?.emailVerified}\n`;

    if (tokenInfo) {
      message += `- EmailVerified in JWT: ${tokenInfo.emailVerified}\n`;
      message += `- Token expired: ${tokenInfo.isExpired}\n`;
      message += `- Token claims: ${JSON.stringify(tokenInfo.claims, null, 2)}`;
    } else {
      message += `- Token: Không thể decode`;
    }

    // Copy to clipboard
    navigator.clipboard.writeText(message);

    this.snackBar.open(
      "Debug info đã copy vào clipboard! Vui lòng gửi cho developer.",
      "Đóng",
      {
        duration: 5000,
      }
    );

    console.log("Debug Info:", { user, tokenInfo });
  }

  resendVerificationEmail(): void {
    if (!this.currentUser?.email) {
      this.snackBar.open("Không tìm thấy email", "Đóng", { duration: 3000 });
      return;
    }

    this.authService
      .resendVerification({ email: this.currentUser.email })
      .subscribe({
        next: (response) => {
          this.snackBar.open(response.message, "Đóng", { duration: 5000 });
        },
        error: (error) => {
          const message =
            error.error?.message || "Không thể gửi lại email xác nhận";
          this.snackBar.open(message, "Đóng", { duration: 3000 });
        },
      });
  }

  syncWithToken(): void {
    this.authService.syncUserWithToken();
    this.snackBar.open("Đã sync với JWT token!", "Đóng", { duration: 3000 });
  }

  getBreadcrumbItems(): any[] {
    const url = this.router.url;
    const items = [];

    if (url.includes('/books')) {
      items.push({ label: 'Quản lý Sách', icon: 'menu_book', active: true });
      if (url.includes('/add')) {
        items.push({ label: 'Thêm sách mới', icon: 'add', active: true });
      } else if (url.includes('/edit')) {
        items.push({ label: 'Chỉnh sửa sách', icon: 'edit', active: true });
      }
    } else if (url.includes('/categories')) {
      items.push({ label: 'Quản lý Thể loại', icon: 'category', active: true });
    } else if (url.includes('/members')) {
      items.push({ label: 'Quản lý Thành viên', icon: 'people', active: true });
    } else if (url.includes('/borrows')) {
      items.push({ label: 'Mượn/Trả sách', icon: 'assignment_turned_in', active: true });
    } else if (url.includes('/shelves')) {
      items.push({ label: 'Quản lý Kệ sách', icon: 'inventory', active: true });
    } else if (url.includes('/reports')) {
      items.push({ label: 'Báo cáo', icon: 'assessment', active: true });
    } else if (url.includes('/dashboard')) {
      items.push({ label: 'Dashboard', icon: 'dashboard', active: true });
    }

    return items;
  }
}
