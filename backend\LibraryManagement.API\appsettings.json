{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=LibraryManagementDb;Trusted_Connection=True;MultipleActiveResultSets=true"}, "Jwt": {"Key": "ThisIsMySecretKeyForJwtAuthenticationWith256BitsLength", "Issuer": "LibraryManagementAPI", "Audience": "LibraryManagementClient", "ExpiryInDays": 7}, "Email": {"FromEmail": "<EMAIL>", "FromName": "<PERSON><PERSON> thống <PERSON> lý <PERSON> viện", "SmtpHost": "smtp.gmail.com", "SmtpPort": "587", "SmtpUsername": "<EMAIL>", "SmtpPassword": "wjxc zacx eaaf httu"}, "Frontend": {"BaseUrl": "http://localhost:4202"}, "Cloudinary": {"CloudName": "dopkhpcy4", "ApiKey": "781352354125167", "ApiSecret": "XEMp2o_54m9gh18KtF7chLpBu4k", "UploadFolder": "library-management"}}